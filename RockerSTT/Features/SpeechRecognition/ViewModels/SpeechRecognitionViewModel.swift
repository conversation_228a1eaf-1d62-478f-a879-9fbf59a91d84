//
//  SpeechRecognitionViewModel.swift
//  RockerSTT
//
//  Created by Rocker on 2025/6/28.
//

import Foundation
import SwiftUI
import AVFoundation
import Combine

// MARK: - Data Models

enum RecognizedLanguage: String, CaseIterable {
    case english = "en"
    case chinese = "zh"
    case cantonese = "yue"
    case japanese = "ja"
    case unknown = "unknown"

    var displayName: String {
        switch self {
        case .english: return "EN"
        case .chinese: return "中文"
        case .cantonese: return "粵語"
        case .japanese: return "日本語"
        case .unknown: return "?"
        }
    }


}

enum EmotionLevel: String, CaseIterable {
    case neutral = "NEUTRAL"
    case happy = "HAPPY"
    case sad = "SAD"
    case unknown = "EMO_UNKNOWN"

    var emoji: String {
        switch self {
        case .neutral: return "😐"
        case .happy: return "😊"
        case .sad: return "😢"
        case .unknown: return "🤔"
        }
    }
}

enum AudioType: String, CaseIterable {
    case speech = "Speech"
    case bgm = "BGM"
    case unknown = "Unknown"
}

struct ParsedContent {
    let language: RecognizedLanguage
    let emotion: EmotionLevel
    let audioType: AudioType
    let cleanText: String
}

class TranscriptionEntry: ObservableObject, Identifiable, Equatable {
    let id = UUID()
    let timestamp: Date
    @Published var text: String
    @Published var parsedContent: ParsedContent?
    @Published var isWaiting: Bool = false
    @Published var isFinal: Bool = false

    // Translation properties
    @Published var translatedText: String?
    @Published var translationState: TranslationState = .idle
    @Published var targetLanguage: TranslationLanguage?

    init(timestamp: Date, text: String, parsedContent: ParsedContent?, isWaiting: Bool = false, isFinal: Bool = false, translatedText: String? = nil, targetLanguage: TranslationLanguage? = nil) {
        self.timestamp = timestamp
        self.text = text
        self.parsedContent = parsedContent
        self.isWaiting = isWaiting
        self.isFinal = isFinal
        self.translatedText = translatedText
        self.targetLanguage = targetLanguage
    }

    var formattedTimestamp: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .medium
        formatter.dateStyle = .none
        return formatter.string(from: timestamp)
    }

    func relativeTimestamp(from startTime: Date) -> String {
        let interval = timestamp.timeIntervalSince(startTime)
        let minutes = Int(interval) / 60
        let seconds = Int(interval) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }

    var displayText: String {
        return parsedContent?.cleanText ?? text
    }

    static func == (lhs: TranscriptionEntry, rhs: TranscriptionEntry) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - FunASR Data Models

struct TranscriptionResponse: Codable {
    let text: String
    let timestamp: Double?
    let mode: String
    let isFinal: Bool

    enum CodingKeys: String, CodingKey {
        case text
        case timestamp
        case mode
        case isFinal = "is_final"
    }
}

enum ASRMode: String, CaseIterable {
    case twoPass = "2pass"
    case online = "online"
    case offline = "offline"

    var displayName: String {
        switch self {
        case .twoPass: return "2-Pass"
        case .online: return "Online"
        case .offline: return "Offline"
        }
    }
}

struct FunASRConfiguration: Codable {
    let chunkSize: [Int]
    let wavName: String
    let isSpeaking: Bool
    let chunkInterval: Int
    let encoderChunkLookBack: Int
    let decoderChunkLookBack: Int
    let enablePreprocessing: Bool
    let preprocessingMode: String
    let itn: Bool
    let mode: String

    enum CodingKeys: String, CodingKey {
        case chunkSize = "chunk_size"
        case wavName = "wav_name"
        case isSpeaking = "is_speaking"
        case chunkInterval = "chunk_interval"
        case encoderChunkLookBack = "encoder_chunk_look_back"
        case decoderChunkLookBack = "decoder_chunk_look_back"
        case enablePreprocessing = "enable_preprocessing"
        case preprocessingMode = "preprocessing_mode"
        case itn
        case mode
    }
}

@MainActor
class SpeechRecognitionViewModel: ObservableObject {
    @Published var isRecording = false
    @Published var connectionState: WebSocketConnectionState = .disconnected(reason: nil)
    @Published var transcriptionEntries: [TranscriptionEntry] = []
    @Published var errorMessage: String?
    @Published var permissionGranted = false
    @Published var networkStatus: NetworkStatus = .unknown

    private let audioEngine = AudioEngineManager()
    private let webSocketManager = WebSocketManager()
    private let hapticFeedback = HapticFeedbackManager.shared
    private let historyStorageService = HistoryStorageService()
    private let networkReachability = NetworkReachabilityManager.shared

    // Smart Word Merging components
    private let smartWordMergingPreferences = SmartWordMergingPreferences.shared
    internal lazy var smartWordMerger = SmartWordMerger(configuration: smartWordMergingPreferences.getCurrentConfiguration())
    private let languageDetector = LanguageDetector()

    // Translation service
    var translationService = HybridTranslationService()

    // Publisher for text updates to trigger auto-scroll
    private let textUpdateSubject = PassthroughSubject<Void, Never>()
    var textUpdatePublisher: AnyPublisher<Void, Never> {
        textUpdateSubject.eraseToAnyPublisher()
    }

    // Text accumulation variables (adapted from JavaScript logic)
    private var offlineText = ""  // for offline rec asr result (accumulates across sessions)
    private var onlineText = ""      // for online display text
    private var activeSessionEntryId: UUID?  // Track current transcription session entry ID
    private var meetingStartTime: Date?  // Track meeting start time for relative timestamps
    
    // Smart Word Merging state tracking
    private var previousPartialResult: String?  // Track previous partial for merging

    // Combine cancellables
    private var cancellables = Set<AnyCancellable>()

    // Settings
    @AppStorage("websocket_url") var websocketURL: String = "ws://rockerwww.ddns.net:10096/"
    @AppStorage("asr_mode") var asrMode: String = ASRMode.twoPass.rawValue
    @AppStorage("use_itn") var useITN: Bool = false
    
    init() {
        setupDelegates()
        loadSettings()
        setupSmartWordMergingPreferences()
        setupNetworkMonitoring()
    }

    private func setupSmartWordMergingPreferences() {
        // Listen for preference changes and update the merger configuration
        smartWordMergingPreferences.objectWillChange
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.updateSmartWordMergerConfiguration()
            }
            .store(in: &cancellables)
    }

    internal func updateSmartWordMergerConfiguration() {
        let newConfiguration = smartWordMergingPreferences.getCurrentConfiguration()
        smartWordMerger = SmartWordMerger(configuration: newConfiguration)

        // Performance monitoring has been removed for simplicity

        if newConfiguration.enableDebugLogging {
            print("🔧 Updated SmartWordMerger configuration: \(newConfiguration)")
        }
    }
    
    private func setupDelegates() {
        audioEngine.delegate = self
        webSocketManager.delegate = self
    }
    
    private func loadSettings() {
        webSocketManager.serverURL = websocketURL
    }

    private func setupNetworkMonitoring() {
        // Start network monitoring
        networkReachability.startMonitoring()

        // Monitor network status changes and update our published property
        networkReachability.$networkStatus
            .receive(on: DispatchQueue.main)
            .assign(to: \.networkStatus, on: self)
            .store(in: &cancellables)

        print("🌐 SpeechRecognitionViewModel: Network monitoring setup complete")
    }
    
    func requestPermissions() async {
        let granted = await audioEngine.requestMicrophonePermission()
        permissionGranted = granted

        if !granted {
            errorMessage = "Microphone permission is required for speech recognition"
        }
    }

    var meetingStartTimeForRelativeTimestamp: Date? {
        return meetingStartTime
    }
    
    func startRecording() {
        // If permissions not granted, request them first and then start recording
        guard permissionGranted else {
            Task {
                await requestPermissions()
                // After requesting permissions, try to start recording again if granted
                if permissionGranted {
                    startRecording()
                }
            }
            return
        }

        // Clear previous transcriptions and reset text accumulators (like JavaScript clear())
        transcriptionEntries = []
        errorMessage = nil
        offlineText = ""
        onlineText = ""
        activeSessionEntryId = nil
        previousPartialResult = nil

        // Start recording asynchronously to avoid blocking UI
        Task {
            await startRecordingAsync()
        }
    }

    @MainActor
    private func startRecordingAsync() async {
        do {
            print("🎬 ViewModel: Starting recording process...")

            // Create FunASR configuration
            let config = createFunASRConfiguration()
            print("⚙️ ViewModel: Created FunASR config - Mode: \(config.mode), ITN: \(config.itn)")

            // Connect to WebSocket with FunASR configuration
            print("🌐 ViewModel: Connecting to WebSocket: \(websocketURL)")
            webSocketManager.connect(to: websocketURL, with: config)

            // Start audio recording on background thread
            print("🎤 ViewModel: Starting audio recording...")
            let audioEngineRef = audioEngine // Capture reference
            try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
                DispatchQueue.global(qos: .userInitiated).async {
                    do {
                        try audioEngineRef.startRecording()
                        print("✅ ViewModel: Audio recording started successfully")
                        continuation.resume()
                    } catch {
                        print("❌ ViewModel: Failed to start audio recording: \(error)")
                        continuation.resume(throwing: error)
                    }
                }
            }

            // Update UI on main thread
            isRecording = true
            
            // Trigger haptic feedback for recording start
            hapticFeedback.recordingStarted()

            // Initialize meeting start time for relative timestamps
            if meetingStartTime == nil {
                meetingStartTime = Date()
                print("⏰ ViewModel: Meeting start time initialized")
            }

            // Create initial waiting entry for first transcription (only for offline modes)
            createWaitingEntry()

            print("✅ ViewModel: Recording state updated")

        } catch {
            // Handle errors on main thread
            errorMessage = "Failed to start recording: \(error.localizedDescription)"
            isRecording = false
            
            // Trigger haptic feedback for error
            hapticFeedback.errorOccurred()
        }
    }
    
    func stopRecording() {
        audioEngine.stopRecording()
        webSocketManager.disconnect(reason: .userInitiated)
        isRecording = false

        // Trigger haptic feedback for recording stop
        hapticFeedback.recordingEnded()

        // Save session to history before clearing data
        saveSessionToHistory()

        // Remove any remaining waiting entries when stopping
        transcriptionEntries.removeAll { $0.isWaiting }

        // Reset text accumulators and session tracking (like JavaScript clear())
        offlineText = ""
        onlineText = ""
        activeSessionEntryId = nil
        previousPartialResult = nil

        print("🛑 ViewModel: Recording stopped, removed waiting entries, reset accumulators and merging state")
    }

    // MARK: - History Management

    /// Saves the current transcription session to history
    private func saveSessionToHistory() {
        // Only save if we have a meeting start time and transcription entries
        guard let startTime = meetingStartTime,
              !transcriptionEntries.isEmpty else {
            print("⚠️ ViewModel: No session to save (no start time or entries)")
            return
        }

        // Filter out waiting entries - include both final and non-final entries with actual content
        let validEntries = transcriptionEntries.filter { entry in
            !entry.isWaiting && !entry.text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        }

        guard !validEntries.isEmpty else {
            print("⚠️ ViewModel: No valid entries to save to history")
            return
        }

        // Mark all valid entries as final when saving to history
        // This ensures they get saved even if the WebSocket didn't send a final response
        for entry in validEntries {
            entry.isFinal = true
        }

        // Save session to history
        historyStorageService.saveCurrentSession(
            transcriptionEntries: validEntries,
            startTime: startTime,
            shouldAutoSave: false // Don't auto-mark as saved, let user decide
        )

        print("✅ ViewModel: Saved session to history with \(validEntries.count) entries (marked as final)")

        // Debug: Print entry details
        for (index, entry) in validEntries.enumerated() {
            print("  Entry \(index + 1): '\(entry.text)' (isFinal: \(entry.isFinal))")
        }
    }

    // MARK: - Debug Methods

    /// Creates a test session for debugging history functionality
    func createTestSession() {
        print("🧪 ViewModel: Creating test session for debugging")

        // Create test entries
        let testEntries = [
            TranscriptionEntry(
                timestamp: Date().addingTimeInterval(-60),
                text: "This is a test transcription entry",
                parsedContent: ParsedContent(
                    language: .english,
                    emotion: .neutral,
                    audioType: .speech,
                    cleanText: "This is a test transcription entry"
                ),
                isWaiting: false,
                isFinal: true
            ),
            TranscriptionEntry(
                timestamp: Date().addingTimeInterval(-30),
                text: "This is another test entry",
                parsedContent: ParsedContent(
                    language: .english,
                    emotion: .happy,
                    audioType: .speech,
                    cleanText: "This is another test entry"
                ),
                isWaiting: false,
                isFinal: true
            )
        ]

        // Save test session
        historyStorageService.saveCurrentSession(
            transcriptionEntries: testEntries,
            startTime: Date().addingTimeInterval(-120),
            shouldAutoSave: false
        )

        print("🧪 ViewModel: Test session created with \(testEntries.count) entries")
    }

    /// Generates session metadata for categorization
    private func generateSessionMetadata() -> (title: String, category: SessionCategory) {
        let finalizedEntries = transcriptionEntries.filter { !$0.isWaiting && $0.isFinal }

        // Generate title from first few words
        let title: String
        if let firstEntry = finalizedEntries.first,
           !firstEntry.text.isEmpty {
            let words = firstEntry.text.components(separatedBy: .whitespacesAndNewlines)
                .filter { !$0.isEmpty }
                .prefix(4)
            title = words.isEmpty ? "Untitled Session" : words.joined(separator: " ")
        } else {
            title = "Untitled Session"
        }

        // Determine category based on content and duration
        let category: SessionCategory
        let sessionDuration = Date().timeIntervalSince(meetingStartTime ?? Date())
        let totalWords = finalizedEntries.reduce(0) { count, entry in
            count + entry.text.components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }.count
        }

        if sessionDuration > 300 && totalWords > 100 { // 5+ minutes and 100+ words
            category = .saved
        } else if finalizedEntries.count > 5 {
            category = .recent
        } else {
            category = .recent
        }

        return (title: title, category: category)
    }

    /// Session categories for organization
    enum SessionCategory {
        case recent
        case saved
        case favorite
    }

    func updateWebSocketURL(_ url: String) {
        websocketURL = url
        webSocketManager.serverURL = url
    }
    
    func clearTranscriptions() {
        // Clear like JavaScript clear() function
        transcriptionEntries = []
        onlineText = ""
        offlineText = ""
        activeSessionEntryId = nil
        previousPartialResult = nil
        print("🧹 ViewModel: Cleared all transcriptions and reset text accumulators and merging state")
    }

    func testConnection(to url: String) {
        let config = createFunASRConfiguration()
        webSocketManager.connect(to: url, with: config)
    }

    func disconnectWebSocket() {
        webSocketManager.disconnect(reason: .userInitiated)
    }

    // MARK: - Text Parsing Logic

    /// Parses FunASR response text to extract language, emotion, and audio type
    /// Format: <|language|><|emotion|><|type|> actual_text
    private func parseResponseText(_ text: String) -> ParsedContent {
        print("🔍 ViewModel: Parsing text: \(text)")

        // Regular expression to match the pattern <|tag|>
        let pattern = #"<\|([^|]+)\|>"#
        let regex = try! NSRegularExpression(pattern: pattern, options: [])
        let range = NSRange(location: 0, length: text.utf16.count)
        let matches = regex.matches(in: text, options: [], range: range)

        var extractedTags: [String] = []
        var cleanText = text

        // Extract all tags and remove them from the text
        for match in matches.reversed() { // Reverse to maintain string indices
            if let tagRange = Range(match.range(at: 1), in: text) {
                let tag = String(text[tagRange])
                extractedTags.insert(tag, at: 0) // Insert at beginning to maintain order
                print("📋 ViewModel: Extracted tag: \(tag)")
            }

            // Remove the entire <|tag|> from the text
            if let fullRange = Range(match.range, in: text) {
                cleanText.removeSubrange(fullRange)
            }
        }

        // Clean up the text (remove leading/trailing whitespace)
        cleanText = cleanText.trimmingCharacters(in: .whitespacesAndNewlines)

        // Parse the extracted tags
        let language: RecognizedLanguage
        let emotion: EmotionLevel
        let audioType: AudioType

        // First tag should be language
        if extractedTags.count > 0 {
            language = RecognizedLanguage(rawValue: extractedTags[0]) ?? .unknown
        } else {
            language = .unknown
        }

        // Second tag should be emotion
        if extractedTags.count > 1 {
            emotion = EmotionLevel(rawValue: extractedTags[1]) ?? .unknown
        } else {
            emotion = .unknown
        }

        // Third tag should be audio type
        if extractedTags.count > 2 {
            audioType = AudioType(rawValue: extractedTags[2]) ?? .unknown
        } else {
            audioType = .unknown
        }

        let parsed = ParsedContent(
            language: language,
            emotion: emotion,
            audioType: audioType,
            cleanText: cleanText
        )

        print("✅ ViewModel: Parsed content - Language: \(language.displayName), Emotion: \(emotion.emoji), Type: \(audioType.rawValue), Text: '\(cleanText)'")

        return parsed
    }

    // MARK: - FunASR Configuration

    private func createFunASRConfiguration() -> FunASRConfiguration {
        return FunASRConfiguration(
            chunkSize: [5, 10, 5],
            wavName: "ios",
            isSpeaking: true,
            chunkInterval: 10,
            encoderChunkLookBack: 4,
            decoderChunkLookBack: 1,
            enablePreprocessing: true,
            preprocessingMode: "meeting_room",
            itn: useITN,
            mode: asrMode
        )
    }


    
    /// Creates a waiting entry that shows animation until real content arrives
    /// Only creates if there isn't already a waiting entry and ASR mode is offline
    private func createWaitingEntry() {
        // Only show waiting entries for offline modes
        let isOfflineMode = asrMode == ASRMode.offline.rawValue

        if !isOfflineMode {
            print("⏳ ViewModel: Skipping waiting entry creation for online mode")
            return
        }

        // Check if the last entry is already a waiting entry
        if let lastEntry = transcriptionEntries.last, lastEntry.isWaiting {
            print("⏳ ViewModel: Waiting entry already exists, skipping creation")
            return
        }

        let waitingEntry = TranscriptionEntry(
            timestamp: Date(),
            text: "...",
            parsedContent: nil,
            isWaiting: true,
            isFinal: false
        )
        transcriptionEntries.append(waitingEntry)
        textUpdateSubject.send()
        print("⏳ ViewModel: Created new waiting entry for offline mode")
    }

    private func addTranscriptionEntry(_ response: TranscriptionResponse) {
        // Use provided timestamp or current time
        let currentTimestamp = Date(timeIntervalSince1970: response.timestamp ?? Date().timeIntervalSince1970)

        // Skip empty responses
        guard !response.text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }

        // Parse the response text to extract language, emotion, and clean text
        let parsedContent = parseResponseText(response.text)

        // Determine ASR mode behavior (adapted from JavaScript getJsonMessage logic)
        let isOfflineMode = response.mode == "2pass-offline" || response.mode == "offline"

        print("🔄 ViewModel: Processing response - Mode: \(response.mode), IsOffline: \(isOfflineMode), Final: \(response.isFinal)")
        print("📝 ViewModel: Response text: '\(response.text)'")
        print("📊 ViewModel: BEFORE - Entries count: \(transcriptionEntries.count), Active session: \(activeSessionEntryId?.uuidString ?? "none")")
        print("📊 ViewModel: BEFORE - recText: '\(onlineText)', offlineText: '\(offlineText)'")

        var displayText = ""

        if isOfflineMode {
            // Offline mode: Use response text directly without merging
            offlineText = response.text
            displayText = offlineText
            onlineText = ""
            // Clear previous partial result for offline mode
            previousPartialResult = nil
            print("📝 ViewModel: Offline mode - offline_text: '\(offlineText)', online_text: '\(onlineText)'")
        } else {
            // Online mode: Apply smart word merging for partial results
            displayText = processPartialResultWithMerging(response)
            onlineText = displayText
            print("📝 ViewModel: Online mode - online_text: '\(onlineText)' (after merging)")
        }

        print("📊 ViewModel: AFTER - online_text: '\(onlineText)', offline_text: '\(offlineText)',displayText: '\(displayText)'")

        // Update or create transcription entry
        updateCurrentTranscriptionEntry(
            displayText: displayText,
            timestamp: currentTimestamp,
            parsedContent: parseResponseText(displayText),
            isOfflineMode: isOfflineMode,
            isFinal: response.isFinal
        )
        
        // Trigger haptic feedback for transcription received
        hapticFeedback.transcriptionReceived()

        print("📊 ViewModel: FINAL - Entries count: \(transcriptionEntries.count), Active session: \(activeSessionEntryId?.uuidString ?? "none")")

    }



    /// Process partial result with smart word merging for online mode
    /// - Parameter response: The transcription response to process
    /// - Returns: The processed text with word merging applied
    private func processPartialResultWithMerging(_ response: TranscriptionResponse) -> String {
        // Parse the response text to get clean text and language
        let parsedContent = parseResponseText(response.text)
        let currentPartialText = parsedContent.cleanText
        
        // Detect language for merging decisions
        let detectedLanguage = parsedContent.language != .unknown ? 
            parsedContent.language : 
            languageDetector.detectLanguageFromText(currentPartialText)
        
        print("🔀 ViewModel: Processing partial with merging - Language: \(detectedLanguage.displayName), Previous: '\(previousPartialResult ?? "none")', Current: '\(currentPartialText)'")
        
        // Apply smart word merging
        let mergedResult = smartWordMerger.mergePartialResults(
            previousPartial: previousPartialResult,
            currentPartial: currentPartialText,
            language: detectedLanguage
        )
        
        // Update previous partial result for next iteration
        updatePreviousPartialResult(mergedResult, isFinal: response.isFinal)
        
        print("🔀 ViewModel: Merging result: '\(mergedResult)'")
        
        return mergedResult
    }
    
    /// Update the previous partial result for word merging
    /// - Parameters:
    ///   - result: The current result text
    ///   - isFinal: Whether this is a final result
    private func updatePreviousPartialResult(_ result: String, isFinal: Bool) {
        if isFinal {
            // Clear previous partial on final results to start fresh for next session
            previousPartialResult = nil
            print("🔀 ViewModel: Cleared previous partial (final result)")
        } else {
            // Store current result as previous for next partial
            previousPartialResult = result
            print("🔀 ViewModel: Updated previous partial: '\(result)'")
        }
    }

    /// Update or create the current transcription entry (strict online/offline logic)
    private func updateCurrentTranscriptionEntry(
        displayText: String,
        timestamp: Date,
        parsedContent: ParsedContent,
        isOfflineMode: Bool,
        isFinal: Bool
    ) {
        // Remove any waiting entry first
        if let lastEntry = transcriptionEntries.last, lastEntry.isWaiting {
            transcriptionEntries.removeLast()
            print("🗑️ ViewModel: Removed waiting entry")
        }

        if isOfflineMode {
            // Offline mode: Finalize current session with final result
            if let activeEntryId = activeSessionEntryId {
                // Find and update the current session entry
                if let entryIndex = transcriptionEntries.firstIndex(where: { $0.id == activeEntryId }) {
                    // Update existing entry - SwiftUI will automatically detect @Published changes
                    transcriptionEntries[entryIndex].text = displayText
                    transcriptionEntries[entryIndex].parsedContent = parsedContent
                    transcriptionEntries[entryIndex].isWaiting = false
                    transcriptionEntries[entryIndex].isFinal = isFinal

                    // Trigger auto-scroll
                    textUpdateSubject.send()

                    print("🔄 ViewModel: Offline mode - finalized existing entry (ID: \(activeEntryId))")

                    // Trigger translation for finalized offline result
                    requestTranslation(for: transcriptionEntries[entryIndex])
                } else {
                    // Shouldn't happen, but create new entry if session entry not found
                    let newEntry = TranscriptionEntry(
                        timestamp: timestamp,
                        text: displayText,
                        parsedContent: parsedContent,
                        isWaiting: false,
                        isFinal: isFinal
                    )
                    transcriptionEntries.append(newEntry)
                    textUpdateSubject.send()
                    print("📝 ViewModel: Offline mode - created new entry (session not found)")

                    // Trigger translation for finalized offline result
                    if isFinal {
                        requestTranslation(for: newEntry)
                    }
                }
            } else {
                // No current session, create new entry
                let newEntry = TranscriptionEntry(
                    timestamp: timestamp,
                    text: displayText,
                    parsedContent: parsedContent,
                    isWaiting: false,
                    isFinal: isFinal
                )
                transcriptionEntries.append(newEntry)
                textUpdateSubject.send()
                print("📝 ViewModel: Offline mode - created new entry (no session)")

                // Trigger translation for finalized offline result
                requestTranslation(for: newEntry)
            }

            // End current session
            finalizeCurrentSession()

        } else {
            // Online mode: Update existing entry or create new one
            if let activeEntryId = activeSessionEntryId {
                // Update existing session entry
                if let entryIndex = transcriptionEntries.firstIndex(where: { $0.id == activeEntryId }) {
                    // Update existing entry - SwiftUI will automatically detect @Published changes
                    transcriptionEntries[entryIndex].text = displayText
                    transcriptionEntries[entryIndex].parsedContent = parsedContent
                    transcriptionEntries[entryIndex].isWaiting = false
                    transcriptionEntries[entryIndex].isFinal = isFinal

                    // Trigger auto-scroll
                    textUpdateSubject.send()

                    print("🔄 ViewModel: Online mode - updated existing entry (ID: \(activeEntryId))")
                } else {
                    // Session ID exists but entry not found, create new entry
                    let newEntry = TranscriptionEntry(
                        timestamp: timestamp,
                        text: displayText,
                        parsedContent: parsedContent,
                        isWaiting: false,
                        isFinal: isFinal
                    )
                    transcriptionEntries.append(newEntry)
                    activeSessionEntryId = newEntry.id
                    textUpdateSubject.send()
                    print("📝 ViewModel: Online mode - created new entry (entry not found, ID: \(newEntry.id))")
                }
            } else {
                // No current session, start new one
                let newEntry = TranscriptionEntry(
                    timestamp: timestamp,
                    text: displayText,
                    parsedContent: parsedContent,
                    isWaiting: false,
                    isFinal: isFinal
                )
                transcriptionEntries.append(newEntry)
                activeSessionEntryId = newEntry.id
                textUpdateSubject.send()
                print("📝 ViewModel: Online mode - started new session (ID: \(newEntry.id))")
            }
        }
    }

    /// Finalize current session and prepare for next one
    private func finalizeCurrentSession() {
        // Reset session tracking - this allows next online message to create a new entry
        activeSessionEntryId = nil
        
        // Clear previous partial result for next session
        previousPartialResult = nil

        // Create waiting entry for next transcription
        createWaitingEntry()
        print("🔄 ViewModel: Session finalized, cleared merging state, ready for next transcription")
    }

    // MARK: - Testing Support
    
    /// Public method to process transcription responses for testing purposes
    /// This method provides access to the private addTranscriptionEntry method for the test utility
    /// - Parameter response: The TranscriptionResponse to process
    func processTranscriptionForTesting(_ response: TranscriptionResponse) {
        addTranscriptionEntry(response)
    }

    // MARK: - Translation Methods

    /// Retries translation for a failed transcription entry
    func retryTranslation(for entry: TranscriptionEntry) {
        print("🔄 ViewModel: Retrying translation for entry: \(entry.id)")
        requestTranslation(for: entry)
    }

    /// Requests translation for a finalized transcription entry
    private func requestTranslation(for entry: TranscriptionEntry) {
        // Only translate if translation is enabled and we have text
        guard translationService.isTranslationEnabled,
              !entry.displayText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            print("🌐 ViewModel: Translation skipped - disabled or empty text")
            return
        }

        // Set translation state to translating
        entry.translationState = .translating
        entry.targetLanguage = translationService.selectedTargetLanguage

        print("🌐 ViewModel: Starting translation for entry: '\(entry.displayText)'")

        Task {
            do {
                // Determine source language from parsed content or detect it
                let sourceLanguage: TranslationLanguage
                if let recognizedLanguage = entry.parsedContent?.language,
                   let translationLanguage = TranslationLanguage.from(recognizedLanguage: recognizedLanguage) {
                    sourceLanguage = translationLanguage
                } else {
                    // Fallback to language detection
                    let detection = try await translationService.detectLanguage(text: entry.displayText)
                    sourceLanguage = detection.language
                    print("🔍 ViewModel: Detected source language: \(sourceLanguage.displayName) (confidence: \(detection.confidence))")
                }

                // Perform translation
                let translatedText = try await translationService.translate(
                    text: entry.displayText,
                    from: sourceLanguage,
                    to: translationService.selectedTargetLanguage
                )

                // Update entry with translation result
                await MainActor.run {
                    entry.translatedText = translatedText
                    entry.translationState = .completed(translatedText)
                    print("✅ ViewModel: Translation completed: '\(translatedText)'")
                }

            } catch {
                // Handle translation error
                await MainActor.run {
                    entry.translationState = .failed(error.localizedDescription)
                    print("❌ ViewModel: Translation failed: \(error.localizedDescription)")
                }
            }
        }
    }


}

// MARK: - AudioEngineManagerDelegate
extension SpeechRecognitionViewModel: AudioEngineManagerDelegate {
    nonisolated func audioEngineManager(_ manager: AudioEngineManager, didCaptureAudioData data: Data) {
        // Send audio data to WebSocket
        webSocketManager.sendAudioData(data)
    }

    nonisolated func audioEngineManager(_ manager: AudioEngineManager, didEncounterError error: Error) {
        Task { @MainActor in
            errorMessage = "Audio error: \(error.localizedDescription)"
            
            // Trigger haptic feedback for error
            hapticFeedback.errorOccurred()

            // Stop recording on error
            if isRecording {
                stopRecording()
            }
        }
    }
}

// MARK: - WebSocketManagerDelegate
extension SpeechRecognitionViewModel: WebSocketManagerDelegate {
    nonisolated func webSocketManager(_ manager: WebSocketManager, didReceiveTranscription transcription: TranscriptionResponse) {
        Task { @MainActor in
            addTranscriptionEntry(transcription)
        }
    }

    nonisolated func webSocketManager(_ manager: WebSocketManager, didChangeConnectionState state: WebSocketConnectionState) {
        Task { @MainActor in
            self.connectionState = state

            switch state {
            case .disconnected(let reason):
                // Only show error messages for non-user-initiated disconnections
                if let reason = reason, reason != .userInitiated {
                    // Show appropriate error message based on disconnection reason
                    switch reason {
                    case .networkError:
                        errorMessage = "Network connection lost"
                    case .serverError:
                        errorMessage = "Server unavailable"
                    case .connectionTimeout:
                        errorMessage = "Connection timeout"
                    case .protocolError:
                        errorMessage = "Communication error"
                    default:
                        errorMessage = "Connection error"
                    }
                } else {
                    // Clear error message for user-initiated disconnections
                    errorMessage = nil
                }

                if isRecording {
                    // If we were recording and got disconnected, stop recording
                    audioEngine.stopRecording()
                    isRecording = false
                }
                // Trigger haptic feedback for disconnection
                hapticFeedback.connectionStateChanged(isConnected: false)

            case .connecting:
                errorMessage = nil

            case .connected:
                errorMessage = nil
                // Trigger haptic feedback for successful connection
                hapticFeedback.connectionStateChanged(isConnected: true)

            case .reconnecting:
                errorMessage = nil
                // Don't show error messages during reconnection attempts
            }
        }
    }

    nonisolated func webSocketManager(_ manager: WebSocketManager, didEncounterError error: Error) {
        Task { @MainActor in
            // Only show error messages for non-user-initiated errors
            // Check if this is a WebSocketError that shouldn't show messages
            if let webSocketError = error as? WebSocketError {
                switch webSocketError {
                case .userCancelled:
                    // Don't show error message for user-cancelled operations
                    errorMessage = nil
                    return
                case .maxReconnectAttemptsReached:
                    errorMessage = "Unable to reconnect. Please check your connection and try again."
                case .connectionTimeout:
                    errorMessage = "Connection timeout. Please check your network."
                case .serverUnreachable:
                    errorMessage = "Server unreachable. Please try again later."
                case .networkUnavailable:
                    errorMessage = "Network unavailable. Please check your connection."
                case .invalidURL:
                    errorMessage = "Invalid server URL. Please check settings."
                default:
                    errorMessage = "Connection error: \(error.localizedDescription)"
                }
            } else {
                errorMessage = "Connection error: \(error.localizedDescription)"
            }

            // Trigger haptic feedback for error
            hapticFeedback.errorOccurred()
        }
    }


}

// MARK: - Helper Extensions
extension WebSocketConnectionState {
    var displayText: String {
        switch self {
        case .disconnected(let reason):
            if let reason = reason {
                switch reason {
                case .userInitiated:
                    return "Stopped"
                case .networkError:
                    return "Network Error"
                case .noNetworkConnection:
                    return "No Internet"
                case .serverError:
                    return "Server Error"
                case .connectionTimeout:
                    return "Connection Timeout"
                case .protocolError:
                    return "Protocol Error"
                case .unknown:
                    return "Disconnected"
                }
            } else {
                return "Disconnected"
            }
        case .connecting:
            return "Connecting..."
        case .connected:
            return "Connected"
        case .reconnecting(let attempt, let maxAttempts):
            if let attempt = attempt, let maxAttempts = maxAttempts {
                return "Reconnecting (\(attempt)/\(maxAttempts))..."
            } else {
                return "Reconnecting..."
            }
        }
    }

    var color: Color {
        switch self {
        case .disconnected(let reason):
            if let reason = reason {
                switch reason {
                case .userInitiated:
                    return .gray // Neutral color for user actions
                case .networkError, .connectionTimeout, .protocolError:
                    return .red // Red for errors
                case .noNetworkConnection:
                    return .orange // Orange for no internet
                case .serverError:
                    return .orange // Orange for server issues
                case .unknown:
                    return .gray // Neutral for unknown
                }
            } else {
                return .gray
            }
        case .connecting, .reconnecting:
            return .orange
        case .connected:
            return .green
        }
    }
}

// MARK: - TranslationLanguage Extension

extension TranslationLanguage {
    /// Create TranslationLanguage from RecognizedLanguage
    static func from(recognizedLanguage: RecognizedLanguage) -> TranslationLanguage? {
        switch recognizedLanguage {
        case .english: return .english
        case .chinese: return .chineseSimplified
        case .cantonese: return .cantonese
        case .japanese: return .japanese
        case .unknown: return nil
        }
    }
}
