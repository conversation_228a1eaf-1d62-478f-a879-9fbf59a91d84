//
//  TranscriptionCard.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/1/15.
//

import SwiftUI

/// Modern card component for displaying transcription entries with enhanced visual design
struct TranscriptionCard: View {
    @ObservedObject var entry: TranscriptionEntry
    let meetingStartTime: Date?
    let connectionState: WebSocketConnectionState
    let viewModel: SpeechRecognitionViewModel?
    
    // Animation and interaction state
    @State private var isPressed = false
    @State private var hasAppeared = false
    @State private var isHighlighted = false
    @State private var currentTime = Date()
    
    // Timer for real-time timestamp updates
    private let timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()
    
    var body: some View {
        cardContent
            .padding(DesignSystem.spacing.xSmall)
//            .background(brandCardBackground)
//            .cornerRadius(CornerRadius.large) // Use design system corner radius
//            .modernShadow(elevation: .medium) // Modern shadow with brand colors
//            .overlay(
//                RoundedRectangle(cornerRadius: CornerRadius.large)
//                    .stroke(
//                        LinearGradient(
//                            gradient: Gradient(colors: [
//                                DesignSystem.brandColors.adaptiveTertiary.opacity(0.3),
//                                DesignSystem.brandColors.adaptiveSecondary.opacity(0.1)
//                            ]),
//                            startPoint: .topLeading,
//                            endPoint: .bottomTrailing
//                        ),
//                        lineWidth: 1
//                    )
//            )
            .textureOverlay() // Add subtle texture
            .modifier(CardAnimationModifier(
                isPressed: isPressed,
                hasAppeared: hasAppeared
            ))
            .onAppear {
                // Staggered entrance animation
                withAnimation(DesignSystem.animations.cardEntrance.delay(0.1)) {
                    hasAppeared = true
                }
            }
            .modifier(CardInteractionModifier(
                isPressed: $isPressed,
                isHighlighted: $isHighlighted
            ))
            .onReceive(timer) { _ in
                // Update current time for real-time relative timestamp display
                currentTime = Date()
            }
            .contextMenu {
                translationContextMenu
            }
    }
    
    private var cardContent: some View {
        VStack(alignment: .leading, spacing: DesignSystem.spacing.xSmall) {
            // Header with avatar, timestamp, and metadata badges
            headerView
            
            // Main transcription text content
            contentView
            
            // Footer with additional metadata (if available)
            if shouldShowFooter {
                footerView
            }
        }
    }
    
    // MARK: - Header View
    
    private var headerView: some View {
        HStack(spacing: DesignSystem.spacing.small) {
            // Avatar with dynamic color based on content
//            avatarView
            
            // Timestamp
//            timestampView
            
            Spacer()

            // Translation button (when applicable)
//            if let viewModel = viewModel, viewModel.translationService.isTranslationEnabled {
//                translationButton
//            }

            // Metadata badges
//            metadataBadges
        }
    }

    private var translationButton: some View {
        Button(action: {
            HapticPattern.light.trigger()
            viewModel?.retryTranslation(for: entry)
        }) {
            Image(systemName: getTranslationButtonIcon())
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(getTranslationButtonColor())
                .frame(width: 24, height: 24)
                .background(
                    Circle()
                        .fill(getTranslationButtonColor().opacity(0.1))
                )
        }
        .buttonStyle(PlainButtonStyle())
        .opacity(shouldShowTranslationButton ? 1.0 : 0.0)
        .scaleEffect(shouldShowTranslationButton ? 1.0 : 0.8)
        .animation(.easeInOut(duration: 0.2), value: shouldShowTranslationButton)
    }

    private var shouldShowTranslationButton: Bool {
        guard let viewModel = viewModel, viewModel.translationService.isTranslationEnabled else {
            return false
        }

        // Show button if:
        // 1. No translation exists yet, OR
        // 2. Translation failed, OR
        // 3. Card is highlighted/hovered
        if case .failed = entry.translationState {
            return true
        }
        return entry.translatedText == nil || isHighlighted
    }

    private func getTranslationButtonIcon() -> String {
        if case .translating = entry.translationState {
            return "arrow.clockwise"
        } else if case .failed = entry.translationState {
            return "exclamationmark.triangle"
        } else if entry.translatedText != nil {
            return "arrow.clockwise"
        } else {
            return "translate"
        }
    }

    private func getTranslationButtonColor() -> Color {
        if case .failed = entry.translationState {
            return DesignSystem.brandColors.amber // Use Amber for errors/warnings
        } else if case .translating = entry.translationState {
            return DesignSystem.brandColors.orchid.opacity(0.7) // Orchid for processing states
        } else {
            return DesignSystem.brandColors.orchid // Orchid for secondary actions
        }
    }

    private var avatarView: some View {
        ZStack {
            Circle()
                .fill(avatarBackgroundColor)
                .frame(width: 32, height: 32)
            
            Image(systemName: avatarIcon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(avatarIconColor)
        }
    }
    
    private var timestampView: some View {
        HStack(spacing: DesignSystem.spacing.micro) {
            // Clock icon for better visual hierarchy
//            Image(systemName: "clock")
//                .font(.system(size: 9, weight: .medium))
//                .foregroundColor(DesignSystem.brandColors.persianPurple) // Persian Purple for metadata
//                .scaleEffect(1.0)
//                .animation(DesignSystem.animations.quick, value: displayTimestamp)
            
            // Enhanced timestamp with relative time display and smooth transitions
            Text(displayTimestamp)
                .font(DesignSystem.typography.timestampText)
                .foregroundColor(DesignSystem.brandColors.persianPurple) // Persian Purple for timestamps
                .monospacedDigit() // Ensures consistent width for time updates
                .contentTransition(.numericText(value: Double(displayTimestamp.hash)))
                .animation(DesignSystem.animations.standard, value: displayTimestamp)
        }
        .padding(.horizontal, 8)
//        .padding(.vertical, 3)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(brandTimestampBackgroundColor)
                .animation(DesignSystem.animations.quick, value: brandTimestampBackgroundColor)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 6)
                .stroke(brandTimestampBorderColor, lineWidth: 0.5)
                .animation(DesignSystem.animations.quick, value: brandTimestampBorderColor)
        )
        .scaleEffect(hasAppeared ? 1.0 : 0.9)
        .opacity(hasAppeared ? 1.0 : 0.0)
        .animation(DesignSystem.animations.cardEntrance.delay(0.05), value: hasAppeared)
    }
    
    private var metadataBadges: some View {
        HStack(spacing: DesignSystem.spacing.micro) {
            // Connection status indicator (only show if not connected)
            if connectionState != .connected && connectionState.isUserInitiated() {
                ConnectionStatusBadge(connectionState: connectionState)
                    .transition(.scale.combined(with: .opacity))
            }
            
            // Language badge
            if let language = entry.parsedContent?.language, language != .unknown {
                BadgeView(text: language.displayName, style: .language)
                    .transition(.scale.combined(with: .opacity))
            }
            
            // Emotion badge
//            if let emotion = entry.parsedContent?.emotion, emotion != .neutral && emotion != .unknown {
//                BadgeView(text: emotion.emoji, style: .emotion)
//                    .transition(.scale.combined(with: .opacity))
//            }
            
            // Audio type indicator
            if let audioType = entry.parsedContent?.audioType, audioType != .speech {
                BadgeView(text: audioTypeIcon(audioType), style: .audioType)
                    .transition(.scale.combined(with: .opacity))
            }
        }
        .animation(DesignSystem.animations.standard, value: connectionState)
        .animation(DesignSystem.animations.standard, value: entry.parsedContent?.language)
        .animation(DesignSystem.animations.standard, value: entry.parsedContent?.emotion)
        .animation(DesignSystem.animations.standard, value: entry.parsedContent?.audioType)
    }
    
    // MARK: - Content View
    
    private var contentView: some View {
        VStack(alignment: .leading, spacing: DesignSystem.spacing.micro) {
            // Original transcription text with proper text hierarchy
            Text(entry.displayText)
                .font(DesignSystem.typography.bodyPrimary) // Enhanced typography for better hierarchy
                .foregroundColor(Color(.label)) // Proper contrast for accessibility
                .lineSpacing(6) // Improved line spacing for readability
                .textSelection(.enabled)
                .fixedSize(horizontal: false, vertical: true)
                .animation(DesignSystem.animations.standard, value: entry.displayText)

            // Translation section
            if let translatedText = entry.translatedText, !translatedText.isEmpty {
                translationView(translatedText: translatedText)
            } else if case .translating = entry.translationState {
                translationLoadingView
            } else if case .failed(let error) = entry.translationState {
                translationErrorView(error: error)
            }

            // Waiting indicator for incomplete transcriptions
            if entry.isWaiting {
                waitingIndicator
            }
        }
    }
    
    private var waitingIndicator: some View {
        HStack(spacing: DesignSystem.spacing.micro) {
            ForEach(0..<3, id: \.self) { index in
                Circle()
                    .fill(DesignSystem.brandColors.orchid.opacity(0.6)) // Use brand Orchid color
                    .frame(width: 4, height: 4)
                    .scaleEffect(entry.isWaiting ? 1.0 : 0.5)
                    .animation(
                        Animation.easeInOut(duration: 0.6)
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.2),
                        value: entry.isWaiting
                    )
            }

            Text("Processing...")
                .font(DesignSystem.typography.captionPrimary) // Use semantic typography
                .foregroundColor(DesignSystem.brandColors.persianPurple) // Persian Purple for metadata
        }
        .padding(.top, DesignSystem.spacing.micro)
    }

    // MARK: - Translation Views

    private func translationView(translatedText: String) -> some View {
        VStack(alignment: .leading, spacing: DesignSystem.spacing.micro) {
            // Translation header with language indicator
            HStack(spacing: DesignSystem.spacing.micro) {
                Image(systemName: "translate")
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(DesignSystem.brandColors.adaptiveSecondary) // Use adaptive secondary

                if let targetLanguage = entry.targetLanguage {
                    Text(targetLanguage.flag)
                        .font(.system(size: 12))

                    Text(targetLanguage.displayName)
                        .font(DesignSystem.typography.captionSecondary)
                        .foregroundColor(DesignSystem.brandColors.adaptivePrimary) // Adaptive primary for metadata
                }

                Spacer()
            }

            // Translated text with proper hierarchy
            Text(translatedText)
                .font(DesignSystem.typography.bodySecondary) // Secondary body for translated content
                .foregroundColor(Color(.secondaryLabel)) // Proper contrast for accessibility
                .lineSpacing(5) // Improved line spacing
                .textSelection(.enabled)
                .fixedSize(horizontal: false, vertical: true)
                .padding(.leading, 16) // Indent to show it's translated content
        }
        .padding(.top, DesignSystem.spacing.micro)
        .transition(.opacity.combined(with: .move(edge: .top)))
    }

    private var translationLoadingView: some View {
        HStack(spacing: DesignSystem.spacing.micro) {
            Image(systemName: "translate")
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(DesignSystem.brandColors.orchid) // Use brand Orchid

            // Animated dots for translation loading
            HStack(spacing: 2) {
                ForEach(0..<3, id: \.self) { index in
                    Circle()
                        .fill(DesignSystem.brandColors.orchid.opacity(0.6)) // Use brand Orchid
                        .frame(width: 3, height: 3)
                        .scaleEffect(1.0)
                        .animation(
                            Animation.easeInOut(duration: 0.5)
                                .repeatForever(autoreverses: true)
                                .delay(Double(index) * 0.15),
                            value: entry.translationState
                        )
                }
            }

            Text("Translating...")
                .font(DesignSystem.typography.captionSecondary)
                .foregroundColor(DesignSystem.brandColors.persianPurple) // Persian Purple for metadata

            Spacer()
        }
        .padding(.top, DesignSystem.spacing.micro)
        .transition(.opacity.combined(with: .move(edge: .top)))
    }

    private func translationErrorView(error: String) -> some View {
        HStack(spacing: DesignSystem.spacing.micro) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(DesignSystem.brandColors.amber) // Use Amber for errors

            Text("Translation failed")
                .font(DesignSystem.typography.captionSecondary)
                .foregroundColor(DesignSystem.brandColors.amber) // Use Amber for errors

            Spacer()

            // Retry button with brand styling
            Button(action: {
                // Trigger retry translation
                retryTranslation()
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: 10, weight: .medium))

                    Text("Retry")
                        .font(DesignSystem.typography.captionSecondary)
                }
                .foregroundColor(DesignSystem.brandColors.adaptivePrimary) // Adaptive primary for actions
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 4)
                        .fill(DesignSystem.brandColors.adaptiveTertiary.opacity(0.3)) // Adaptive tertiary background
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.top, DesignSystem.spacing.micro)
        .transition(.opacity.combined(with: .move(edge: .top)))
    }

    private func retryTranslation() {
        // Trigger haptic feedback
        HapticPattern.light.trigger()

        // Request translation retry through viewModel
        viewModel?.retryTranslation(for: entry)
    }

    // MARK: - Context Menu

    @ViewBuilder
    private var translationContextMenu: some View {
        if let viewModel = viewModel {
            // Translation actions
            if viewModel.translationService.isTranslationEnabled {
                let isTranslationFailed = { if case .failed = entry.translationState { return true }; return false }()
                if entry.translatedText == nil || isTranslationFailed {
                    Button(action: {
                        HapticPattern.light.trigger()
                        viewModel.retryTranslation(for: entry)
                    }) {
                        Label("Translate", systemImage: "translate")
                    }
                }

                if entry.translatedText != nil {
                    Button(action: {
                        HapticPattern.light.trigger()
                        viewModel.retryTranslation(for: entry)
                    }) {
                        Label("Re-translate", systemImage: "arrow.clockwise")
                    }
                }

                // Language selection submenu
                Menu {
                    ForEach(TranslationLanguage.allCases.prefix(8)) { language in
                        Button(action: {
                            HapticPattern.light.trigger()
                            let previousLanguage = viewModel.translationService.selectedTargetLanguage
                            viewModel.translationService.selectedTargetLanguage = language
                            viewModel.retryTranslation(for: entry)
                            // Restore previous language for other translations
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                viewModel.translationService.selectedTargetLanguage = previousLanguage
                            }
                        }) {
                            HStack {
                                Text("\(language.flag)   \(language.displayName)")
                                if language == viewModel.translationService.selectedTargetLanguage {
                                    Spacer()
                                    Image(systemName: "checkmark")
                                        .foregroundColor(.blue)
                                }
                            }
                        }
                    }

                    if TranslationLanguage.allCases.count > 8 {
                        Menu("More Languages") {
                            ForEach(TranslationLanguage.allCases.dropFirst(8)) { language in
                                Button(action: {
                                    HapticPattern.light.trigger()
                                    let previousLanguage = viewModel.translationService.selectedTargetLanguage
                                    viewModel.translationService.selectedTargetLanguage = language
                                    viewModel.retryTranslation(for: entry)
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                        viewModel.translationService.selectedTargetLanguage = previousLanguage
                                    }
                                }) {
                                    Text("\(language.flag)   \(language.displayName)")
                                }
                            }
                        }
                    }
                } label: {
                    Label("Translate to...", systemImage: "globe")
                }
            } else {
                Button(action: {
                    HapticPattern.light.trigger()
                    viewModel.translationService.isTranslationEnabled = true
                    viewModel.retryTranslation(for: entry)
                }) {
                    Label("Enable Translation", systemImage: "translate")
                }
            }

            Divider()

            // Copy actions
            Button(action: {
                HapticPattern.light.trigger()
                UIPasteboard.general.string = entry.displayText
            }) {
                Label("Copy Original", systemImage: "doc.on.doc")
            }

            if let translatedText = entry.translatedText {
                Button(action: {
                    HapticPattern.light.trigger()
                    UIPasteboard.general.string = translatedText
                }) {
                    Label("Copy Translation", systemImage: "doc.on.doc.fill")
                }

                Button(action: {
                    HapticPattern.light.trigger()
                    let combinedText = "\(entry.displayText)\n\n\(translatedText)"
                    UIPasteboard.general.string = combinedText
                }) {
                    Label("Copy Both", systemImage: "doc.on.clipboard")
                }
            }
        }
    }

    // MARK: - Footer View
    
    private var footerView: some View {
        HStack(spacing: DesignSystem.spacing.small) {
            // Left side metadata
            HStack(spacing: DesignSystem.spacing.small) {
                // Final status indicator with enhanced styling
//                if entry.isFinal {
//                    HStack(spacing: DesignSystem.spacing.micro) {
//                        Image(systemName: "checkmark.circle.fill")
//                            .font(.system(size: 10, weight: .medium))
//                            .foregroundColor(DesignSystem.brandColors.orchid) // Use brand Orchid for success
//                        
//                        Text("Final")
//                            .font(DesignSystem.typography.captionSecondary)
//                            .foregroundColor(DesignSystem.brandColors.persianPurple) // Persian Purple for metadata
//                    }
//                    .padding(.horizontal, 6)
//                    .padding(.vertical, 2)
//                    .background(
//                        RoundedRectangle(cornerRadius: 3)
//                            .fill(DesignSystem.brandColors.frenchLilac.opacity(0.3)) // French Lilac background
//                    )
//                    .transition(.scale.combined(with: .opacity))
//                    .animation(DesignSystem.animations.standard, value: entry.isFinal)
//                }
                
                // Audio quality indicator (if available)
                if let parsedContent = entry.parsedContent {
                    AudioQualityIndicator(audioType: parsedContent.audioType)
                        .transition(.scale.combined(with: .opacity))
                        .animation(DesignSystem.animations.standard, value: parsedContent.audioType)
                }
            }
            
            Spacer()
            
            // Right side metadata
            HStack(spacing: DesignSystem.spacing.small) {
                // Processing time indicator (for performance feedback)
//                if let processingTime = calculateProcessingTime() {
//                    HStack(spacing: 2) {
//                        Image(systemName: "timer")
//                            .font(.system(size: 8, weight: .medium))
//                            .foregroundColor(DesignSystem.brandColors.persianPurple) // Persian Purple for metadata
//                        
//                        Text(processingTime)
//                            .font(DesignSystem.typography.captionSecondary)
//                            .foregroundColor(DesignSystem.brandColors.persianPurple) // Persian Purple for metadata
//                            .monospacedDigit()
//                    }
//                    .transition(.opacity.combined(with: .move(edge: .trailing)))
//                    .animation(DesignSystem.animations.standard, value: processingTime)
//                }
                
                // Connection quality indicator (when not connected)
                if connectionState != .connected && connectionState.isUserInitiated(){
                    ConnectionQualityIndicator(connectionState: connectionState)
                        .transition(.scale.combined(with: .opacity))
                        .animation(DesignSystem.animations.standard, value: connectionState)
                }
            }
        }
        .padding(.top, DesignSystem.spacing.micro)
        .opacity(shouldShowFooter ? 1.0 : 0.0)
        .animation(DesignSystem.animations.standard, value: shouldShowFooter)
    }
    
    // MARK: - Computed Properties
    
    private var displayTimestamp: String {
        if let meetingStartTime = meetingStartTime {
            // Use current time for real-time relative timestamp updates
            let interval = currentTime.timeIntervalSince(entry.timestamp)
            if interval < 60 {
                return "now"
            } else if interval < 3600 {
                let minutes = Int(interval) / 60
                return "\(minutes)m ago"
            } else {
                return entry.relativeTimestamp(from: meetingStartTime)
            }
        } else {
            return entry.formattedTimestamp
        }
    }
    
    private var shouldShowFooter: Bool {
//        entry.isFinal || entry.parsedContent != nil
        false
    }
    
    // MARK: - Brand-specific styling properties
    
    private var brandCardBackground: Color {
        // Adaptive background that responds to light/dark mode
        if isHighlighted {
            return DesignSystem.brandColors.adaptiveCardBackground.opacity(0.95)
        } else {
            return DesignSystem.brandColors.adaptiveCardBackground
        }
    }

    private var brandShadowColor: Color {
        // Adaptive shadow effects with brand colors
        isPressed ? Color.clear : DesignSystem.brandColors.adaptivePrimary.opacity(0.08)
    }
    
    private var brandShadowRadius: CGFloat {
        isPressed ? 2 : 6 // Subtle shadow effects
    }
    
    private var brandShadowOffset: CGFloat {
        isPressed ? 1 : 3
    }
    
    private var brandTimestampBackgroundColor: Color {
        if entry.isFinal {
            return DesignSystem.brandColors.adaptiveTertiary.opacity(0.3)
        } else if entry.isWaiting {
            return DesignSystem.brandColors.adaptiveAccent.opacity(0.15)
        } else {
            return DesignSystem.brandColors.adaptiveTertiary.opacity(0.2)
        }
    }

    private var brandTimestampBorderColor: Color {
        if entry.isFinal {
            return DesignSystem.brandColors.adaptiveSecondary.opacity(0.3)
        } else if entry.isWaiting {
            return DesignSystem.brandColors.adaptiveAccent.opacity(0.4)
        } else {
            return DesignSystem.brandColors.adaptiveSecondary.opacity(0.2)
        }
    }
    
    // Legacy properties for backward compatibility
    private var cardBackground: Color {
        brandCardBackground
    }
    
    private var shadowColor: Color {
        brandShadowColor
    }
    
    private var shadowRadius: CGFloat {
        brandShadowRadius
    }
    
    private var shadowOffset: CGFloat {
        brandShadowOffset
    }
    
    private var avatarBackgroundColor: Color {
        guard let parsedContent = entry.parsedContent else {
            return DesignSystem.brandColors.adaptiveSecondary.opacity(0.15) // Use adaptive colors
        }

        switch parsedContent.emotion {
        case .happy:
            return DesignSystem.brandColors.adaptiveTertiary.opacity(0.4) // Positive emotions with adaptive tertiary
        case .sad:
            return DesignSystem.brandColors.adaptiveAccent.opacity(0.2) // Warnings/negative with adaptive accent
        case .neutral, .unknown:
            return DesignSystem.brandColors.adaptiveSecondary.opacity(0.15) // Default with adaptive secondary
        }
    }
    
    private var avatarIconColor: Color {
        guard let parsedContent = entry.parsedContent else {
            return DesignSystem.brandColors.orchid // Use brand colors
        }
        
        switch parsedContent.emotion {
        case .happy:
            return DesignSystem.brandColors.persianPurple // Persian Purple for positive
        case .sad:
            return DesignSystem.brandColors.amber // Amber for warnings
        case .neutral, .unknown:
            return DesignSystem.brandColors.orchid // Orchid for neutral
        }
    }
    
    private var avatarIcon: String {
        guard let audioType = entry.parsedContent?.audioType else {
            return "person.circle.fill"
        }
        
        switch audioType {
        case .speech:
            return "person.circle.fill"
        case .bgm:
            return "music.note.circle.fill"
        case .unknown:
            return "questionmark.circle.fill"
        }
    }
    
    private var timestampBackgroundColor: Color {
        brandTimestampBackgroundColor
    }
    
    private var timestampBorderColor: Color {
        brandTimestampBorderColor
    }
    
    private func audioTypeIcon(_ audioType: AudioType) -> String {
        switch audioType {
        case .speech:
            return "💬"
        case .bgm:
            return "🎵"
        case .unknown:
            return "❓"
        }
    }
    
    /// Calculate processing time for performance feedback
    private func calculateProcessingTime() -> String? {
        // Only show processing time for final entries
        guard entry.isFinal else { return nil }
        
        // Simulate processing time calculation (in a real app, this would be tracked)
        // For now, we'll show a placeholder based on text length
        let textLength = entry.displayText.count
        let estimatedTime = max(0.1, Double(textLength) / 100.0) // Rough estimate
        
        if estimatedTime < 1.0 {
            return String(format: "%.1fs", estimatedTime)
        } else {
            return String(format: "%.0fs", estimatedTime)
        }
    }
}

// MARK: - Connection Status Badge Component

struct ConnectionStatusBadge: View {
    let connectionState: WebSocketConnectionState
    
    var body: some View {
        HStack(spacing: 3) {
            // Enhanced connection status indicator with icon
            ZStack {
                // Background pulse for connecting states
                if isConnectingOrReconnecting {
                    pulseCircle
                }

                // Main status indicator
                statusIndicator
            }
            
            // Status text with smooth transitions
            Text(statusText)
                .font(DesignSystem.typography.badgeText)
                .foregroundColor(statusColor)
                .transition(.opacity.combined(with: .scale(scale: 0.8)))
                .animation(DesignSystem.animations.standard, value: connectionState)
        }
        .padding(.horizontal, 7)
        .padding(.vertical, 3)
        .background(
            RoundedRectangle(cornerRadius: CornerRadius.badge)
                .fill(statusColor.opacity(0.12))
                .overlay(
                    RoundedRectangle(cornerRadius: CornerRadius.badge)
                        .stroke(statusColor.opacity(0.2), lineWidth: 0.5)
                )
        )
        .animation(DesignSystem.animations.standard, value: connectionState)
    }

    // MARK: - Computed Properties

    private var isConnectingOrReconnecting: Bool {
        connectionState == .connecting || connectionState.isReconnecting()
    }

    private var pulseCircle: some View {
        Circle()
            .fill(statusColor.opacity(0.3))
            .frame(width: 12, height: 12)
            .scaleEffect(1.5)
            .opacity(0.6)
            .animation(
                Animation.easeInOut(duration: 1.2).repeatForever(autoreverses: true),
                value: connectionState
            )
    }

    private var statusIndicator: some View {
        Image(systemName: statusIcon)
            .font(.system(size: 8, weight: .bold))
            .foregroundColor(statusColor)
            .frame(width: 10, height: 10)
            .scaleEffect(isConnectingOrReconnecting ? 1.1 : 1.0)
            .animation(
                isConnectingOrReconnecting
                    ? Animation.easeInOut(duration: 0.8).repeatForever(autoreverses: true)
                    : DesignSystem.animations.quick,
                value: connectionState
            )
    }

    private var statusColor: Color {
        switch connectionState {
        case .disconnected(let reason):
            // Use adaptive brand colors based on disconnection reason
            if let reason = reason {
                switch reason {
                case .userInitiated:
                    return DesignSystem.brandColors.adaptivePrimary // Adaptive primary for user actions
                case .networkError, .connectionTimeout:
                    return DesignSystem.brandColors.adaptiveAccent // Adaptive accent for network issues
                case .noNetworkConnection:
                    return DesignSystem.brandColors.adaptiveAccent // Adaptive accent for no internet
                case .serverError:
                    return DesignSystem.brandColors.adaptiveAccent // Adaptive accent for server issues
                case .protocolError:
                    return DesignSystem.brandColors.adaptiveAccent // Adaptive accent for protocol errors
                case .unknown:
                    return DesignSystem.brandColors.adaptiveSecondary // Adaptive secondary for unknown
                }
            } else {
                return DesignSystem.brandColors.adaptiveSecondary // Adaptive secondary for no reason
            }
        case .connecting, .reconnecting:
            return DesignSystem.brandColors.orchid // Orchid for connecting states
        case .connected:
            return DesignSystem.brandColors.frenchLilac // French Lilac for success (subtle)
        }
    }

    private var statusIcon: String {
        switch connectionState {
        case .disconnected(let reason):
            if let reason = reason {
                switch reason {
                case .userInitiated:
                    return "stop.circle" // User stopped
                case .networkError, .connectionTimeout:
                    return "wifi.slash" // Network issues
                case .noNetworkConnection:
                    return "wifi.exclamationmark" // No internet connection
                case .serverError:
                    return "server.rack" // Server issues
                case .protocolError:
                    return "exclamationmark.triangle" // Protocol errors
                case .unknown:
                    return "questionmark.circle" // Unknown issues
                }
            } else {
                return "wifi.slash" // Default disconnected icon
            }
        case .connecting:
            return "wifi.exclamationmark"
        case .connected:
            return "wifi"
        case .reconnecting:
            return "arrow.clockwise"
        }
    }

    private var statusText: String {
        switch connectionState {
        case .disconnected(let reason):
            if let reason = reason {
                switch reason {
                case .userInitiated:
                    return "Stopped" // User-friendly text for user actions
                case .networkError:
                    return "Network Error"
                case .noNetworkConnection:
                    return "No Internet"
                case .serverError:
                    return "Server Error"
                case .connectionTimeout:
                    return "Timeout"
                case .protocolError:
                    return "Protocol Error"
                case .unknown:
                    return "Disconnected"
                }
            } else {
                return "Offline"
            }
        case .connecting:
            return "Connecting"
        case .connected:
            return "Online"
        case .reconnecting(let attempt, let maxAttempts):
            if let attempt = attempt, let maxAttempts = maxAttempts {
                return "Reconnecting (\(attempt)/\(maxAttempts))"
            } else {
                return "Reconnecting"
            }
        }
    }
}

// MARK: - Audio Quality Indicator Component

struct AudioQualityIndicator: View {
    let audioType: AudioType
    
    var body: some View {
        HStack(spacing: 2) {
            Image(systemName: qualityIcon)
                .font(.system(size: 8, weight: .medium))
                .foregroundColor(qualityColor)
            
            Text(qualityText)
                .font(DesignSystem.typography.caption2)
                .foregroundColor(qualityColor)
        }
        .padding(.horizontal, 5)
        .padding(.vertical, 2)
        .background(
            RoundedRectangle(cornerRadius: 3)
                .fill(qualityColor.opacity(0.1))
        )
    }
    
    private var qualityIcon: String {
        switch audioType {
        case .speech:
            return "waveform"
        case .bgm:
            return "music.note"
        case .unknown:
            return "questionmark.diamond"
        }
    }
    
    private var qualityColor: Color {
        switch audioType {
        case .speech:
            return DesignSystem.brandColors.adaptiveSecondary // Adaptive secondary for speech
        case .bgm:
            return DesignSystem.brandColors.adaptivePrimary // Adaptive primary for music
        case .unknown:
            return DesignSystem.brandColors.adaptiveTertiary // Adaptive tertiary for unknown
        }
    }
    
    private var qualityText: String {
        switch audioType {
        case .speech:
            return "Speech"
        case .bgm:
            return "Music"
        case .unknown:
            return "Mixed"
        }
    }
}

// MARK: - Connection Quality Indicator Component

struct ConnectionQualityIndicator: View {
    let connectionState: WebSocketConnectionState
    
    var body: some View {
        HStack(spacing: 2) {
            // Signal strength bars
            HStack(spacing: 1) {
                ForEach(0..<3, id: \.self) { index in
                    Rectangle()
                        .fill(barColor(for: index))
                        .frame(width: 2, height: CGFloat(4 + index * 2))
                        .opacity(shouldShowBar(for: index) ? 1.0 : 0.3)
                        .animation(DesignSystem.animations.quick, value: connectionState)
                }
            }
            
            Text(connectionText)
                .font(DesignSystem.typography.caption2)
                .foregroundColor(connectionColor)
        }
        .padding(.horizontal, 5)
        .padding(.vertical, 2)
        .background(
            RoundedRectangle(cornerRadius: 3)
                .fill(connectionColor.opacity(0.1))
        )
    }
    
    private func barColor(for index: Int) -> Color {
        switch connectionState {
        case .disconnected:
            return DesignSystem.brandColors.amber // Amber for errors
        case .connecting, .reconnecting:
            return DesignSystem.brandColors.orchid // Orchid for connecting
        case .connected:
            return DesignSystem.brandColors.frenchLilac // French Lilac for success
        }
    }
    
    private func shouldShowBar(for index: Int) -> Bool {
        switch connectionState {
        case .disconnected:
            return false
        case .connecting:
            return index == 0
        case .reconnecting:
            return index <= 1
        case .connected:
            return true
        }
    }
    
    private var connectionColor: Color {
        switch connectionState {
        case .disconnected(let reason):
            if let reason = reason, reason != .userInitiated {
                return DesignSystem.brandColors.adaptiveAccent // Adaptive accent for errors
            } else {
                return DesignSystem.brandColors.adaptivePrimary // Adaptive primary for user actions
            }
        case .connecting, .reconnecting:
            return DesignSystem.brandColors.adaptiveSecondary // Adaptive secondary for connecting
        case .connected:
            return DesignSystem.brandColors.adaptiveTertiary // Adaptive tertiary for success
        }
    }

    private var connectionText: String {
        switch connectionState {
        case .disconnected(let reason):
            if let reason = reason {
                switch reason {
                case .userInitiated:
                    return "Stopped"
                case .networkError, .connectionTimeout:
                    return "No Signal"
                case .noNetworkConnection:
                    return "No Internet"
                case .serverError:
                    return "Server Down"
                case .protocolError:
                    return "Error"
                case .unknown:
                    return "Unknown"
                }
            } else {
                return "No Signal"
            }
        case .connecting:
            return "Weak"
        case .reconnecting:
            return "Poor"
        case .connected:
            return "Strong"
        }
    }
}

// MARK: - Badge View Component

struct BadgeView: View {
    let text: String
    let style: BadgeStyle
    
    enum BadgeStyle {
        case language
        case emotion
        case audioType
        
        var backgroundColor: Color {
            switch self {
            case .language:
                return DesignSystem.brandColors.adaptiveTertiary.opacity(0.4) // Adaptive tertiary for language
            case .emotion:
                return DesignSystem.brandColors.adaptiveAccent.opacity(0.2) // Adaptive accent for emotion
            case .audioType:
                return DesignSystem.brandColors.adaptiveSecondary.opacity(0.15) // Adaptive secondary for audio type
            }
        }

        var textColor: Color {
            switch self {
            case .language:
                return DesignSystem.brandColors.adaptivePrimary // Adaptive primary for language text
            case .emotion:
                return DesignSystem.brandColors.adaptiveAccent // Adaptive accent for emotion text
            case .audioType:
                return DesignSystem.brandColors.adaptiveSecondary // Adaptive secondary for audio type text
            }
        }
    }
    
    var body: some View {
        Text(text)
            .font(DesignSystem.typography.badgeText)
            .foregroundColor(style.textColor)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(style.backgroundColor)
            .cornerRadius(CornerRadius.badge)
    }
}

// MARK: - View Modifiers

struct CardAnimationModifier: ViewModifier {
    let isPressed: Bool
    let hasAppeared: Bool
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .opacity(hasAppeared ? 1.0 : 0.0)
            .offset(y: hasAppeared ? 0 : 20)
            .animation(DesignSystem.animations.cardEntrance, value: hasAppeared)
            .animation(DesignSystem.animations.buttonPress, value: isPressed)
    }
}

struct CardInteractionModifier: ViewModifier {
    @Binding var isPressed: Bool
    @Binding var isHighlighted: Bool
    
    func body(content: Content) -> some View {
        content
            .onTapGesture {
                // Provide haptic feedback and visual response
                HapticPattern.selection.trigger()
                withAnimation(DesignSystem.animations.buttonPress) {
                    isPressed = true
                }
                
                // Reset pressed state after brief delay
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation(DesignSystem.animations.buttonPress) {
                        isPressed = false
                    }
                }
            }
            .onLongPressGesture(minimumDuration: 0.5) {
                // Long press for additional interactions (future feature)
                HapticPattern.medium.trigger()
                withAnimation(DesignSystem.animations.standard) {
                    isHighlighted.toggle()
                }
            }
    }
}

// MARK: - Preview

#Preview("Single Card") {
    VStack(spacing: 16) {
        TranscriptionCard(
            entry: TranscriptionEntry(
                timestamp: Date(),
                text: "Hello, this is a test transcription with some longer text to see how it wraps and displays in the card.",
                parsedContent: ParsedContent(
                    language: .english,
                    emotion: .happy,
                    audioType: .speech,
                    cleanText: "Hello, this is a test transcription with some longer text to see how it wraps and displays in the card."
                ),
                isFinal: true
            ),
            meetingStartTime: Date().addingTimeInterval(-300),
            connectionState: .connected,
            viewModel: nil
        )
        
        TranscriptionCard(
            entry: TranscriptionEntry(
                timestamp: Date(),
                text: "这是一个中文测试转录，用来查看卡片如何显示中文内容。",
                parsedContent: ParsedContent(
                    language: .chinese,
                    emotion: .neutral,
                    audioType: .speech,
                    cleanText: "这是一个中文测试转录，用来查看卡片如何显示中文内容。"
                ),
                isWaiting: true
            ),
            meetingStartTime: nil,
            connectionState: .connecting,
            viewModel: nil
        )
        TranscriptionCard(
            entry: TranscriptionEntry(
                timestamp: Date(),
                text: "这是一个中文测试转录，用来查看卡片如何显示中文内容。",
                parsedContent: ParsedContent(
                    language: .chinese,
                    emotion: .neutral,
                    audioType: .speech,
                    cleanText: "这是一个中文测试转录，用来查看卡片如何显示中文内容。"
                ),
                isWaiting: true
            ),
            meetingStartTime: nil,
            connectionState: .connecting,
            viewModel: nil
        )
        TranscriptionCard(
            entry: TranscriptionEntry(
                timestamp: Date(),
                text: "这是一个中文测试转录，用来查看卡片如何显示中文内容。",
                parsedContent: ParsedContent(
                    language: .chinese,
                    emotion: .neutral,
                    audioType: .speech,
                    cleanText: "这是一个中文测试转录，用来查看卡片如何显示中文内容。"
                ),
                isWaiting: true
            ),
            meetingStartTime: nil,
            connectionState: .connecting,
            viewModel: nil
        )
        TranscriptionCard(
            entry: TranscriptionEntry(
                timestamp: Date(),
                text: "这是一个中文测试转录，用来查看卡片如何显示中文内容。",
                parsedContent: ParsedContent(
                    language: .chinese,
                    emotion: .neutral,
                    audioType: .speech,
                    cleanText: "这是一个中文测试转录，用来查看卡片如何显示中文内容。"
                ),
                isWaiting: true
            ),
            meetingStartTime: nil,
            connectionState: .connecting,
            viewModel: nil
        )
        TranscriptionCard(
            entry: TranscriptionEntry(
                timestamp: Date(),
                text: "这是一个中文测试转录，用来查看卡片如何显示中文内容。",
                parsedContent: ParsedContent(
                    language: .chinese,
                    emotion: .neutral,
                    audioType: .speech,
                    cleanText: "这是一个中文测试转录，用来查看卡片如何显示中文内容。"
                ),
                isWaiting: true
            ),
            meetingStartTime: nil,
            connectionState: .connecting,
            viewModel: nil
        )
        TranscriptionCard(
            entry: TranscriptionEntry(
                timestamp: Date(),
                text: "这是一个中文测试转录，用来查看卡片如何显示中文内容。",
                parsedContent: ParsedContent(
                    language: .chinese,
                    emotion: .neutral,
                    audioType: .speech,
                    cleanText: "这是一个中文测试转录，用来查看卡片如何显示中文内容。"
                ),
                isWaiting: true
            ),
            meetingStartTime: nil,
            connectionState: .connecting,
            viewModel: nil
        )
    }
    .padding(DesignSystem.spacing.xSmall)
    .background(DesignSystem.colors.adaptiveBackground)
}

#Preview("Card List") {
    ScrollView {
        LazyVStack(spacing: DesignSystem.spacing.cardSpacing) {
            TranscriptionCard(
                entry: TranscriptionEntry(
                    timestamp: Date(),
                    text: "Sample transcription entry 1.",
                    parsedContent: ParsedContent(
                        language: .english,
                        emotion: .happy,
                        audioType: .speech,
                        cleanText: "Sample transcription entry 1."
                    ),
                    isFinal: true
                ),
                meetingStartTime: Date().addingTimeInterval(-300),
                connectionState: .connected,
                viewModel: nil
            )

            TranscriptionCard(
                entry: TranscriptionEntry(
                    timestamp: Date().addingTimeInterval(-30),
                    text: "Sample transcription entry 2.",
                    parsedContent: ParsedContent(
                        language: .chinese,
                        emotion: .neutral,
                        audioType: .speech,
                        cleanText: "Sample transcription entry 2."
                    ),
                    isFinal: false
                ),
                meetingStartTime: Date().addingTimeInterval(-300),
                connectionState: .connecting,
                viewModel: nil
            )
        }
        .padding()
    }
    .background(DesignSystem.colors.adaptiveBackground)
}
