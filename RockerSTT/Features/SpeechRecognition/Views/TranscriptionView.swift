//
//  TranscriptionView.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/6/28.
//

import SwiftUI

struct TranscriptionView: View {
    @StateObject private var viewModel = SpeechRecognitionViewModel()
    @State private var showingSettings = false
    @State private var showingDeveloperDebug = false
    @State private var scrollDebounceTimer: Timer?

    // Smart auto-scrolling state
    @State private var isUserInteracting: Bool = false
    @State private var userInteractionTimer: Timer?
    @State private var shouldAutoScroll: Bool = false
    @State private var autoScrollTrigger: Int = 0

    // Animation state for recording rings - controlled manually to stop properly
    @State private var ringAnimationScale1: CGFloat = 1.0
    @State private var ringAnimationScale2: CGFloat = 1.0
    @State private var ringAnimationScale3: CGFloat = 1.0
    @State private var ringAnimationOpacity1: Double = 0.0
    @State private var ringAnimationOpacity2: Double = 0.0
    @State private var ringAnimationOpacity3: Double = 0.0

    var body: some View {
        VStack(spacing: 0) {
            // Main transcription area - continuous scrollable text
            transcriptionTextArea

            // Controls
            controlsView
        }
        .brandRecordingGradientBackground()
        .task {
            await viewModel.requestPermissions()
        }
        .onChange(of: viewModel.isRecording) { _, isRecording in
            // Control ring animations manually to ensure they stop properly
            if isRecording {
                startRingAnimations()
            } else {
                stopRingAnimations()
            }
        }
    }



    private var transcriptionTextArea: some View {
        ScrollViewReader { proxy in
            if viewModel.transcriptionEntries.isEmpty {
                // Enhanced empty state
                EmptyStateView(type: determineEmptyStateType())
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                // List view with transcription entries using TranscriptionCard
                List {
                    ForEach(Array(viewModel.transcriptionEntries.enumerated()), id: \.element.id) { index, entry in
                        transcriptionCardView(entry: entry, index: index)
                    }
                }
                .listStyle(.plain)
                .background(Color.clear)
                .animation(AnimationManager.accessibleCardEntrance, value: viewModel.transcriptionEntries.count)
                .onChange(of: viewModel.transcriptionEntries.count) {
                    // Smart auto-scroll when new entries are added
                    if isUserInteracting {
                        shouldAutoScroll = true
                    } else {
                        // Perform immediate auto-scroll
                        scrollDebounceTimer?.invalidate()
                        scrollDebounceTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: false) { _ in
                            if let lastEntry = viewModel.transcriptionEntries.last {
                                DispatchQueue.main.async {
                                    withAnimation(AnimationManager.accessibleButtonPress) {
                                        proxy.scrollTo(lastEntry.id, anchor: .bottom)
                                    }
                                }
                            }
                        }
                    }
                }
                .onReceive(viewModel.textUpdatePublisher) { _ in
                    // Smart auto-scroll when any entry's text is updated
                    if isUserInteracting {
                        shouldAutoScroll = true
                    } else {
                        // Perform immediate auto-scroll
                        scrollDebounceTimer?.invalidate()
                        scrollDebounceTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: false) { _ in
                            if let lastEntry = viewModel.transcriptionEntries.last {
                                DispatchQueue.main.async {
                                    withAnimation(AnimationManager.accessibleButtonPress) {
                                        proxy.scrollTo(lastEntry.id, anchor: .bottom)
                                    }
                                }
                            }
                        }
                    }
                }
                .onChange(of: autoScrollTrigger) {
                    // Handle delayed auto-scroll after user interaction ends
                    if !isUserInteracting && shouldAutoScroll {
                        shouldAutoScroll = false
                        // Perform delayed auto-scroll
                        scrollDebounceTimer?.invalidate()
                        scrollDebounceTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: false) { _ in
                            if let lastEntry = viewModel.transcriptionEntries.last {
                                DispatchQueue.main.async {
                                    withAnimation(AnimationManager.accessibleButtonPress) {
                                        proxy.scrollTo(lastEntry.id, anchor: .bottom)
                                    }
                                }
                            }
                        }
                    }
                }
                .simultaneousGesture(
                    // Detect user interaction with the list
                    DragGesture(minimumDistance: 0)
                        .onChanged { _ in
                            handleUserInteractionStart()
                        }
                        .onEnded { _ in
                            handleUserInteractionEnd()
                        }
                )
            }
        }
    }

    // MARK: - Helper Views

    /// Creates a transcription card view with proper styling and animations
    private func transcriptionCardView(entry: TranscriptionEntry, index: Int) -> some View {
        TranscriptionCard(
            entry: entry,
            meetingStartTime: viewModel.meetingStartTimeForRelativeTimestamp,
            connectionState: viewModel.connectionState,
            viewModel: viewModel
        )
        .listRowSeparator(.hidden)
        .listRowBackground(Color.clear)
        .listRowInsets(EdgeInsets(
            top: 0,
            leading: DesignSystem.spacing.small,
            bottom: DesignSystem.spacing.xSmall,
            trailing: DesignSystem.spacing.small
        ))
        .id(entry.id)
        .transition(cardTransition)
        .animation(
            AnimationManager.cardEntranceStaggered(index: index % 3),
            value: viewModel.transcriptionEntries.count
        )
    }

    /// Transition animation for transcription cards
    private var cardTransition: AnyTransition {
        .asymmetric(
            insertion: AnimationManager.isReducedMotionEnabled
                ? .opacity
                : .scale(scale: 0.8)
                    .combined(with: .opacity)
                    .combined(with: .move(edge: .bottom))
                    .combined(with: .offset(y: 20)),
            removal: AnimationManager.isReducedMotionEnabled
                ? .opacity
                : .scale(scale: 0.8)
                    .combined(with: .opacity)
                    .combined(with: .move(edge: .top))
        )
    }

    private var controlsView: some View {
        VStack(spacing: DesignSystem.spacing.small) {
            HStack {
                #if DEBUG
                // Test Data Button (Debug only)
                Button(action: loadTestData) {
                    HStack(spacing: 4) {
                        Image(systemName: "testtube.2")
                            .font(.system(size: 14, weight: .medium))
                        Text("Test Data")
                            .font(.system(size: 12, weight: .medium))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.blue.opacity(0.8))
                    )
                }
                .accessibilityLabel("Load test transcription data")
                #endif

                Spacer()

                // Enhanced Record button with brand styling and improved animations
                Button(action: recordingButtonAction) {
                    recordingButtonContent
                }
                .buttonStyle(RecordingButtonStyle())
                .disabled(!viewModel.permissionGranted || viewModel.connectionState == .connecting)

                Spacer()

                #if DEBUG
                // Clear Data Button (Debug only)
                Button(action: clearTestData) {
                    HStack(spacing: 4) {
                        Image(systemName: "trash")
                            .font(.system(size: 14, weight: .medium))
                        Text("Clear")
                            .font(.system(size: 12, weight: .medium))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.red.opacity(0.8))
                    )
                }
                .accessibilityLabel("Clear all transcription data")
                #endif
            }
        }
        .padding(DesignSystem.spacing.xSmall)
    }

    // MARK: - Recording Button Components

    private func recordingButtonAction() {
        // Enhanced haptic feedback
        if viewModel.isRecording {
            HapticFeedbackManager.shared.recordingEnded()
            viewModel.stopRecording()
        } else {
            HapticFeedbackManager.shared.recordingStarted()
            viewModel.startRecording()
        }
    }

    private var recordingButtonContent: some View {
        ZStack {
            // Outer animated visualization rings (always present but hidden when not recording)
            // Outer ring with enhanced animation - using amber color to match recording button
            Circle()
                .stroke(DesignSystem.brandColors.amber.opacity(0.3), lineWidth: 2)
                .frame(width: 100, height: 100)
                .scaleEffect(ringAnimationScale1)
                .opacity(ringAnimationOpacity1)

            // Middle ring with staggered animation - using amber color to match recording button
            Circle()
                .stroke(DesignSystem.brandColors.amber.opacity(0.5), lineWidth: 3)
                .frame(width: 88, height: 88)
                .scaleEffect(ringAnimationScale2)
                .opacity(ringAnimationOpacity2)

            // Inner ring with final staggered animation - using amber color to match recording button
            Circle()
                .stroke(DesignSystem.brandColors.amber.opacity(0.7), lineWidth: 2)
                .frame(width: 82, height: 82)
                .scaleEffect(ringAnimationScale3)
                .opacity(ringAnimationOpacity3)

            // Main button circle with Persian Purple gradient and shadow
            Circle()
                .fill(
                    viewModel.isRecording
                        ? AnyShapeStyle(DesignSystem.brandColors.amber.opacity(0.9))
                        : AnyShapeStyle(DesignSystem.brandColors.primaryGradient)
                )
                .frame(width: 76, height: 76) // iPhone 16 optimized size (minimum 44pt touch target)
                .shadow(
                    color: DesignSystem.brandColors.persianPurple.opacity(0.4),
                    radius: 12,
                    x: 0,
                    y: 6
                )
                .overlay(
                    // Subtle inner glow effect
                    Circle()
                        .fill(
                            RadialGradient(
                                gradient: Gradient(colors: [
                                    (viewModel.isRecording
                                        ? DesignSystem.brandColors.amber
                                        : DesignSystem.brandColors.persianPurple).opacity(0.2),
                                    Color.clear
                                ]),
                                center: .center,
                                startRadius: 38,
                                endRadius: 55
                            )
                        )
                        .frame(width: 110, height: 110)
                        .opacity(viewModel.isRecording ? 1.0 : 0.7)
                        .animation(AnimationManager.recordingStateChange, value: viewModel.isRecording)
                )

            // Button content with proper visual feedback states
            Group {
                if viewModel.connectionState == .connecting {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.2)
                } else {
                    Image(systemName: viewModel.isRecording ? "stop.fill" : "mic.fill")
                        .font(.system(size: 28, weight: .medium)) // iPhone 16 optimized icon size
                        .foregroundColor(.white)
                        .scaleEffect(viewModel.isRecording ? 0.9 : 1.0)
                        .animation(AnimationManager.accessibleButtonPress, value: viewModel.isRecording)
                }
            }
        }
        .frame(width: 100, height: 100) // Fixed frame size for consistent positioning
        .accessibilityLabel(recordingButtonAccessibilityLabel)
        .accessibilityHint(recordingButtonAccessibilityHint)
        .accessibilityValue(recordingButtonAccessibilityValue)
        .accessibilityAddTraits(.isButton)
        .accessibilityIdentifier("main_recording_button")
        .scaleEffect(viewModel.isRecording ? 1.05 : 1.0)
        .animation(AnimationManager.recordingStateChange, value: viewModel.isRecording)
    }

    // MARK: - Helper Methods
    
    private func determineEmptyStateType() -> EmptyStateType {
        // Check microphone permission first
        if !viewModel.permissionGranted {
            return .noPermission
        }

        // Check network connectivity using NetworkReachabilityManager
        switch viewModel.networkStatus {
        case .disconnected:
            return .noInternet
        case .unknown:
            // During app startup, allow other states to be shown
            break
        case .connected:
            // Network is available, check other states
            break
        }

        // Check WebSocket connection state (only when network is available)
        if viewModel.connectionState == .connecting {
            return .connecting
        } else if viewModel.isRecording {
            return .listening
        } else if let errorMessage = viewModel.errorMessage {
            return .error(errorMessage)
        } else {
            return .ready
        }
    }

    // MARK: - Smart Auto-Scrolling

    /// Handles the start of user interaction with the list
    private func handleUserInteractionStart() {
        isUserInteracting = true
        userInteractionTimer?.invalidate()
        shouldAutoScroll = false
    }

    /// Handles the end of user interaction with the list
    private func handleUserInteractionEnd() {
        // Start countdown timer before re-enabling auto-scroll
        userInteractionTimer?.invalidate()
        userInteractionTimer = Timer.scheduledTimer(withTimeInterval: 1.5, repeats: false) { _ in
            DispatchQueue.main.async {
                self.isUserInteracting = false

                // If we should auto-scroll, trigger it
                if self.shouldAutoScroll {
                    self.shouldAutoScroll = false
                    self.autoScrollTrigger += 1 // This will trigger the onChange below
                }
            }
        }
    }

    // MARK: - Accessibility Helpers
    
    private var recordingButtonAccessibilityLabel: String {
        if viewModel.connectionState == .connecting {
            return "Connecting to speech recognition service"
        } else if viewModel.isRecording {
            return "Stop recording"
        } else {
            return "Start recording"
        }
    }
    
    private var recordingButtonAccessibilityHint: String {
        if !viewModel.permissionGranted {
            return "Microphone permission required"
        } else if viewModel.connectionState == .connecting {
            return "Please wait while connecting"
        } else if viewModel.isRecording {
            return "Double tap to stop recording your speech"
        } else {
            return "Double tap to start recording your speech"
        }
    }
    
    private var recordingButtonAccessibilityValue: String {
        switch viewModel.connectionState {
        case .connecting:
            return "Connecting"
        case .connected:
            return viewModel.isRecording ? "Recording" : "Ready"
        case .disconnected:
            return "Disconnected"
        case .reconnecting:
            return "Reconnecting"
        }
    }

    // MARK: - Ring Animation Control

    /// Start the recording ring animations with proper staggered timing
    private func startRingAnimations() {
        guard !AnimationManager.isReducedMotionEnabled else { return }

        // Start outer ring animation with more dramatic scaling
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(AnimationManager.recordingRings) {
                ringAnimationScale1 = 1.3
                ringAnimationOpacity1 = 0.8
            }
        }
        
        // Start middle ring animation with delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            withAnimation(AnimationManager.recordingRings) {
                ringAnimationScale2 = 1.15
                ringAnimationOpacity2 = 0.9
            }
        }

        // Start inner ring animation with delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            withAnimation(AnimationManager.recordingRings) {
                ringAnimationScale3 = 1.08
                ringAnimationOpacity3 = 1.0
            }
        }
    }

    /// Stop the recording ring animations immediately
    private func stopRingAnimations() {
        withAnimation(AnimationManager.recordingStateChange) {
            ringAnimationScale1 = 1.0
            ringAnimationScale2 = 1.0
            ringAnimationScale3 = 1.0
            ringAnimationOpacity1 = 0.0
            ringAnimationOpacity2 = 0.0
            ringAnimationOpacity3 = 0.0
        }
    }

    // MARK: - Test Data Functions (Debug Only)

    #if DEBUG
    /// Loads comprehensive test data for UI testing and development
    private func loadTestData() {
        // Clear existing data first
        viewModel.transcriptionEntries.removeAll()

        // Create test entries with various scenarios
        let entry1 = TranscriptionEntry(
            timestamp: Date().addingTimeInterval(-300),
            text: "Hello, I'm currently speaking and this transcription is still being processed...",
            parsedContent: ParsedContent(
                language: .english,
                emotion: .neutral,
                audioType: .speech,
                cleanText: "Hello, I'm currently speaking and this transcription is still being processed"
            ),
            isWaiting: true,
            isFinal: false
        )

        let entry2 = TranscriptionEntry(
            timestamp: Date().addingTimeInterval(-250),
            text: "This is a completed English transcription that is currently being translated to Spanish.",
            parsedContent: ParsedContent(
                language: .english,
                emotion: .happy,
                audioType: .speech,
                cleanText: "This is a completed English transcription that is currently being translated to Spanish."
            ),
            isWaiting: false,
            isFinal: true,
            targetLanguage: .spanish
        )
        entry2.translationState = .translating

        let entry3 = TranscriptionEntry(
            timestamp: Date().addingTimeInterval(-200),
            text: "这是一个中文转录示例，已经完成了英文翻译。",
            parsedContent: ParsedContent(
                language: .chinese,
                emotion: .happy,
                audioType: .speech,
                cleanText: "这是一个中文转录示例，已经完成了英文翻译。"
            ),
            isWaiting: false,
            isFinal: true,
            translatedText: "This is a Chinese transcription example that has completed English translation.",
            targetLanguage: .english
        )
        entry3.translationState = .completed("This is a Chinese transcription example that has completed English translation.")

        let entry4 = TranscriptionEntry(
            timestamp: Date().addingTimeInterval(-150),
            text: "申し訳ございませんが、今日は少し悲しい気分です。",
            parsedContent: ParsedContent(
                language: .japanese,
                emotion: .sad,
                audioType: .speech,
                cleanText: "申し訳ございませんが、今日は少し悲しい気分です。"
            ),
            isWaiting: false,
            isFinal: true
        )

        let entry5 = TranscriptionEntry(
            timestamp: Date().addingTimeInterval(-100),
            text: "This is a much longer transcription entry that contains multiple sentences and should demonstrate how the card handles text wrapping and spacing. It includes various punctuation marks, numbers like 123 and 456, and should show how the layout adapts to longer content while maintaining readability and proper visual hierarchy.",
            parsedContent: ParsedContent(
                language: .english,
                emotion: .neutral,
                audioType: .speech,
                cleanText: "This is a much longer transcription entry that contains multiple sentences and should demonstrate how the card handles text wrapping and spacing. It includes various punctuation marks, numbers like 123 and 456, and should show how the layout adapts to longer content while maintaining readability and proper visual hierarchy."
            ),
            isWaiting: false,
            isFinal: true,
            translatedText: "Esta es una entrada de transcripción mucho más larga que contiene múltiples oraciones...",
            targetLanguage: .spanish
        )
        entry5.translationState = .completed("Esta es una entrada de transcripción mucho más larga que contiene múltiples oraciones y debería demostrar cómo la tarjeta maneja el ajuste de texto y el espaciado.")

        let entry6 = TranscriptionEntry(
            timestamp: Date().addingTimeInterval(-50),
            text: "呢個係粵語嘅測試，情緒識別唔太確定。",
            parsedContent: ParsedContent(
                language: .cantonese,
                emotion: .unknown,
                audioType: .speech,
                cleanText: "呢個係粵語嘅測試，情緒識別唔太確定。"
            ),
            isWaiting: false,
            isFinal: true
        )

        let entry7 = TranscriptionEntry(
            timestamp: Date().addingTimeInterval(-25),
            text: "This transcription had a translation error.",
            parsedContent: ParsedContent(
                language: .english,
                emotion: .neutral,
                audioType: .speech,
                cleanText: "This transcription had a translation error."
            ),
            isWaiting: false,
            isFinal: true,
            targetLanguage: .french
        )
        entry7.translationState = .failed("Translation service temporarily unavailable")

        let entry8 = TranscriptionEntry(
            timestamp: Date(),
            text: "And this is the most recent entry that's currently being transcribed in real-time...",
            parsedContent: ParsedContent(
                language: .english,
                emotion: .happy,
                audioType: .speech,
                cleanText: "And this is the most recent entry that's currently being transcribed in real-time"
            ),
            isWaiting: true,
            isFinal: false
        )

        // Add all entries to the view model
        viewModel.transcriptionEntries = [entry1, entry2, entry3, entry4, entry5, entry6, entry7, entry8]

        // Provide haptic feedback
        HapticFeedbackManager.shared.buttonPressed()

        print("🧪 Test data loaded: \(viewModel.transcriptionEntries.count) entries")
    }

    /// Clears all transcription data for clean testing
    private func clearTestData() {
        viewModel.transcriptionEntries.removeAll()

        // Provide haptic feedback
        HapticFeedbackManager.shared.buttonPressed()

        print("🗑️ Test data cleared")
    }
    #endif


}

// MARK: - Custom Button Style for Recording Button

struct RecordingButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .opacity(configuration.isPressed ? 0.8 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

#Preview {
    TranscriptionView()
}





#Preview("Single Card States") {
    VStack(spacing: 16) {
        // Waiting/Processing state with animation dots
        TranscriptionCard(
            entry: TranscriptionEntry(
                timestamp: Date(),
                text: "Processing speech input...",
                parsedContent: ParsedContent(
                    language: .english,
                    emotion: .neutral,
                    audioType: .speech,
                    cleanText: "Processing speech input"
                ),
                isWaiting: true,
                isFinal: false
            ),
            meetingStartTime: Date().addingTimeInterval(-60),
            connectionState: .connecting,
            viewModel: nil
        )

        // Translation in progress - simplified for preview
        TranscriptionCard(
            entry: TranscriptionEntry(
                timestamp: Date().addingTimeInterval(-30),
                text: "Hello world, how are you today? (Translating...)",
                parsedContent: ParsedContent(
                    language: .english,
                    emotion: .happy,
                    audioType: .speech,
                    cleanText: "Hello world, how are you today?"
                ),
                isWaiting: false,
                isFinal: true,
                targetLanguage: .spanish
            ),
            meetingStartTime: Date().addingTimeInterval(-60),
            connectionState: .connected,
            viewModel: nil
        )

        // Completed with translation - simplified for preview
        TranscriptionCard(
            entry: TranscriptionEntry(
                timestamp: Date().addingTimeInterval(-60),
                text: "Bonjour le monde, comment allez-vous?",
                parsedContent: ParsedContent(
                    language: .unknown,
                    emotion: .happy,
                    audioType: .speech,
                    cleanText: "Bonjour le monde, comment allez-vous?"
                ),
                isWaiting: false,
                isFinal: true,
                translatedText: "Hello world, how are you?",
                targetLanguage: .english
            ),
            meetingStartTime: Date().addingTimeInterval(-120),
            connectionState: .connected,
            viewModel: nil
        )
    }
    .padding()
    .background(DesignSystem.brandColors.adaptiveBackground)
}

#Preview("Emotion Variations") {
    VStack(spacing: 12) {
        // Happy emotion
        TranscriptionCard(
            entry: TranscriptionEntry(
                timestamp: Date(),
                text: "I'm so excited about this new feature! It's working perfectly!",
                parsedContent: ParsedContent(
                    language: .english,
                    emotion: .happy,
                    audioType: .speech,
                    cleanText: "I'm so excited about this new feature! It's working perfectly!"
                ),
                isWaiting: false,
                isFinal: true
            ),
            meetingStartTime: Date().addingTimeInterval(-60),
            connectionState: .connected,
            viewModel: nil
        )

        // Sad emotion
        TranscriptionCard(
            entry: TranscriptionEntry(
                timestamp: Date().addingTimeInterval(-30),
                text: "Unfortunately, we encountered some issues with the deployment.",
                parsedContent: ParsedContent(
                    language: .english,
                    emotion: .sad,
                    audioType: .speech,
                    cleanText: "Unfortunately, we encountered some issues with the deployment."
                ),
                isWaiting: false,
                isFinal: true
            ),
            meetingStartTime: Date().addingTimeInterval(-60),
            connectionState: .connected,
            viewModel: nil
        )

        // Neutral emotion
        TranscriptionCard(
            entry: TranscriptionEntry(
                timestamp: Date().addingTimeInterval(-60),
                text: "The meeting is scheduled for 3 PM tomorrow in conference room B.",
                parsedContent: ParsedContent(
                    language: .english,
                    emotion: .neutral,
                    audioType: .speech,
                    cleanText: "The meeting is scheduled for 3 PM tomorrow in conference room B."
                ),
                isWaiting: false,
                isFinal: true
            ),
            meetingStartTime: Date().addingTimeInterval(-120),
            connectionState: .connected,
            viewModel: nil
        )

        // Unknown emotion
        TranscriptionCard(
            entry: TranscriptionEntry(
                timestamp: Date().addingTimeInterval(-90),
                text: "The system couldn't determine the emotional context of this statement.",
                parsedContent: ParsedContent(
                    language: .english,
                    emotion: .unknown,
                    audioType: .speech,
                    cleanText: "The system couldn't determine the emotional context of this statement."
                ),
                isWaiting: false,
                isFinal: true
            ),
            meetingStartTime: Date().addingTimeInterval(-120),
            connectionState: .connected,
            viewModel: nil
        )
    }
    .padding()
    .background(DesignSystem.brandColors.adaptiveBackground)
}

#Preview("Language Variations") {
    ScrollView {
        VStack(spacing: 12) {
            // English
            TranscriptionCard(
                entry: TranscriptionEntry(
                    timestamp: Date(),
                    text: "This is an English transcription example.",
                    parsedContent: ParsedContent(
                        language: .english,
                        emotion: .neutral,
                        audioType: .speech,
                        cleanText: "This is an English transcription example."
                    ),
                    isWaiting: false,
                    isFinal: true
                ),
                meetingStartTime: Date().addingTimeInterval(-60),
                connectionState: .connected,
                viewModel: nil
            )

            // Chinese Simplified
            TranscriptionCard(
                entry: TranscriptionEntry(
                    timestamp: Date().addingTimeInterval(-20),
                    text: "这是一个中文简体转录示例。",
                    parsedContent: ParsedContent(
                        language: .chinese,
                        emotion: .neutral,
                        audioType: .speech,
                        cleanText: "这是一个中文简体转录示例。"
                    ),
                    isWaiting: false,
                    isFinal: true
                ),
                meetingStartTime: Date().addingTimeInterval(-60),
                connectionState: .connected,
                viewModel: nil
            )

            // Cantonese
            TranscriptionCard(
                entry: TranscriptionEntry(
                    timestamp: Date().addingTimeInterval(-40),
                    text: "呢個係粵語轉錄嘅例子。",
                    parsedContent: ParsedContent(
                        language: .cantonese,
                        emotion: .neutral,
                        audioType: .speech,
                        cleanText: "呢個係粵語轉錄嘅例子。"
                    ),
                    isWaiting: false,
                    isFinal: true
                ),
                meetingStartTime: Date().addingTimeInterval(-60),
                connectionState: .connected,
                viewModel: nil
            )

            // Japanese
            TranscriptionCard(
                entry: TranscriptionEntry(
                    timestamp: Date().addingTimeInterval(-60),
                    text: "これは日本語の転写例です。",
                    parsedContent: ParsedContent(
                        language: .japanese,
                        emotion: .neutral,
                        audioType: .speech,
                        cleanText: "これは日本語の転写例です。"
                    ),
                    isWaiting: false,
                    isFinal: true
                ),
                meetingStartTime: Date().addingTimeInterval(-120),
                connectionState: .connected,
                viewModel: nil
            )
        }
        .padding()
    }
    .background(DesignSystem.brandColors.adaptiveBackground)
}
